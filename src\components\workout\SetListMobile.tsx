'use client'

import type {
  WorkoutLogSerieModelRef,
  ExerciseWorkSetsModel,
  MassUnit,
} from '@/types/api/WorkoutLogSerieModelRef'

interface SetListMobileProps {
  exercise: ExerciseWorkSetsModel
  onSetTap: (set: WorkoutLogSerieModelRef, index: number) => void
  massUnit: MassUnit
}

export function SetListMobile({
  exercise,
  onSetTap,
  massUnit,
}: SetListMobileProps) {
  const formatWeight = (set: WorkoutLogSerieModelRef): string => {
    if (set.IsBodyweight || exercise.IsBodyweight) {
      return 'Bodyweight'
    }

    const weight =
      massUnit === 'kg' ? `${set.Weight.Kg} kg` : `${set.Weight.Lb} lb`

    return weight
  }

  const getSetTypeLabel = (set: WorkoutLogSerieModelRef): string | null => {
    if (set.IsMaxChallenge) return 'MAX'
    if (set.IsDropSet) return 'DROP'
    if (set.IsBackOffSet) return 'BACKOFF'
    return null
  }

  return (
    <div className="space-y-2 px-4">
      {exercise.Sets.map((set, index) => {
        const isHighlighted = set.IsNext || set.IsFinished
        const backgroundColor = isHighlighted
          ? 'bg-primary/10'
          : 'bg-background'

        return (
          <div
            key={set.Id}
            data-testid={`set-${index}`}
            onClick={() => onSetTap(set, index)}
            className={`
              relative rounded-lg border p-4 transition-all
              ${backgroundColor}
              ${set.IsNext ? 'border-primary' : 'border-border'}
              ${set.IsFinished ? 'opacity-75' : ''}
              min-h-[60px] cursor-pointer active:scale-[0.98]
            `}
          >
            {/* Set Title */}
            <div className="flex items-center justify-between mb-1">
              <span
                className={`font-medium ${set.IsWarmups ? 'text-orange-600' : 'text-text-primary'}`}
              >
                {set.SetTitle}
              </span>

              <div className="flex items-center gap-2">
                {getSetTypeLabel(set) && (
                  <span className="text-xs bg-primary/20 text-primary px-2 py-0.5 rounded">
                    {getSetTypeLabel(set)}
                  </span>
                )}

                {set.ShowWorkTimer && !set.IsFinished && (
                  <div
                    className="w-4 h-4 text-text-secondary"
                    data-testid="timer-icon"
                  >
                    ⏱️
                  </div>
                )}

                {set.IsFinished && (
                  <div
                    className="w-4 h-4 text-green-600"
                    data-testid="check-icon"
                  >
                    ✓
                  </div>
                )}
              </div>
            </div>

            {/* Weight × Reps */}
            <div className="text-lg font-semibold">
              {formatWeight(set)} × {set.Reps}
            </div>

            {/* Previous Performance */}
            {set.PreviousReps > 0 && set.LastTimeSet && (
              <div className="text-sm text-text-secondary mt-1">
                Last:{' '}
                {massUnit === 'kg'
                  ? set.PreviousWeight.Kg
                  : set.PreviousWeight.Lb}{' '}
                {massUnit} × {set.PreviousReps}
              </div>
            )}

            {/* RIR Display */}
            {set.RIR !== undefined && set.RIR >= 0 && !set.IsWarmups && (
              <div className="text-sm text-text-secondary">RIR: {set.RIR}</div>
            )}
          </div>
        )
      })}
    </div>
  )
}
