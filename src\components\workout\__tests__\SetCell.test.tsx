import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { SetCell } from '../SetCell'

describe('SetCell', () => {
  describe('Grid Layout', () => {
    it('should render header row with SET | REPS | * | LBS columns', () => {
      render(<SetCell isHeaderCell />)

      expect(screen.getByText('SET')).toBeInTheDocument()
      expect(screen.getByText('REPS')).toBeInTheDocument()
      expect(screen.getByText('LBS')).toBeInTheDocument()
    })

    it('should render set data in grid layout', () => {
      const mockSet = {
        setNo: 1,
        reps: 10,
        weight: 135,
        isFinished: false,
      }

      render(<SetCell {...mockSet} />)

      // Check set number
      expect(screen.getByText('1')).toBeInTheDocument()

      // Check reps input
      const repsInput = screen.getByDisplayValue('10')
      expect(repsInput).toBeInTheDocument()
      expect(repsInput).toHaveAttribute('type', 'number')

      // Check multiplication symbol
      expect(screen.getByText('*')).toBeInTheDocument()

      // Check weight input
      const weightInput = screen.getByDisplayValue('135')
      expect(weightInput).toBeInTheDocument()
      expect(weightInput).toHaveAttribute('type', 'number')
    })
  })

  describe('Inline Editing', () => {
    it('should allow editing reps when set is not finished', () => {
      const onRepsChange = vi.fn()

      render(
        <SetCell
          setNo={1}
          reps={10}
          weight={135}
          isFinished={false}
          onRepsChange={onRepsChange}
        />
      )

      const repsInput = screen.getByDisplayValue('10') as HTMLInputElement

      // Clear and set new value
      fireEvent.change(repsInput, { target: { value: '12' } })

      expect(onRepsChange).toHaveBeenCalledWith(12)
    })

    it('should allow editing weight when set is not finished', () => {
      const onWeightChange = vi.fn()

      render(
        <SetCell
          setNo={1}
          reps={10}
          weight={135}
          isFinished={false}
          onWeightChange={onWeightChange}
        />
      )

      const weightInput = screen.getByDisplayValue('135') as HTMLInputElement

      // Clear and set new value
      fireEvent.change(weightInput, { target: { value: '145' } })

      expect(onWeightChange).toHaveBeenCalledWith(145)
    })

    it('should disable editing when set is finished', () => {
      render(<SetCell setNo={1} reps={10} weight={135} isFinished />)

      const repsInput = screen.getByDisplayValue('10')
      const weightInput = screen.getByDisplayValue('135')

      expect(repsInput).toBeDisabled()
      expect(weightInput).toBeDisabled()
    })

    it('should disable weight input for bodyweight exercises', () => {
      render(
        <SetCell
          setNo={1}
          reps={10}
          weight={0}
          isBodyweight
          isFinished={false}
        />
      )

      const weightInput = screen.getByDisplayValue('0')
      expect(weightInput).toBeDisabled()
    })
  })

  describe('Visual States', () => {
    it('should show check icon when set is finished', () => {
      render(<SetCell setNo={1} reps={10} weight={135} isFinished />)

      const checkIcon = screen.getByTestId('check-icon')
      expect(checkIcon).toBeInTheDocument()
    })

    it('should show delete icon when set is not finished', () => {
      render(<SetCell setNo={1} reps={10} weight={135} isFinished={false} />)

      const deleteIcon = screen.getByTestId('delete-icon')
      expect(deleteIcon).toBeInTheDocument()
    })

    it('should apply finished styles when set is completed', () => {
      const { container } = render(
        <SetCell setNo={1} reps={10} weight={135} isFinished />
      )

      const setRow = container.firstChild
      expect(setRow).toHaveClass('opacity-75')
    })
  })

  describe('Touch Targets', () => {
    it('should have minimum 44px touch targets for inputs', () => {
      render(<SetCell setNo={1} reps={10} weight={135} isFinished={false} />)

      const repsInput = screen.getByDisplayValue('10')
      const weightInput = screen.getByDisplayValue('135')

      // Check minimum height for touch targets via className
      expect(repsInput).toHaveClass('min-h-[44px]')
      expect(weightInput).toHaveClass('min-h-[44px]')
    })
  })

  describe('Last Set Features', () => {
    it('should show "All sets done" message when last set is finished', () => {
      render(
        <SetCell
          setNo={3}
          reps={10}
          weight={135}
          isFinished
          isLastSet
          isExerciseFinished={false}
        />
      )

      expect(screen.getByText('All sets done—congrats!')).toBeInTheDocument()
    })

    it('should show "Finish exercise" button when last set is finished', () => {
      const onFinishExercise = vi.fn()

      render(
        <SetCell
          setNo={3}
          reps={10}
          weight={135}
          isFinished
          isLastSet
          onFinishExercise={onFinishExercise}
        />
      )

      const finishButton = screen.getByText('Finish exercise')
      expect(finishButton).toBeInTheDocument()
    })

    it('should show "Add set" button when on last set but not finished with exercise', () => {
      const onAddSet = vi.fn()

      render(
        <SetCell
          setNo={3}
          reps={10}
          weight={135}
          isFinished={false}
          isLastSet
          isExerciseFinished={false}
          onAddSet={onAddSet}
        />
      )

      const addSetButton = screen.getByText('Add set')
      expect(addSetButton).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for inputs', () => {
      render(<SetCell setNo={1} reps={10} weight={135} isFinished={false} />)

      const repsInput = screen.getByLabelText('Reps for set 1')
      const weightInput = screen.getByLabelText('Weight for set 1')

      expect(repsInput).toBeInTheDocument()
      expect(weightInput).toBeInTheDocument()
    })
  })
})
