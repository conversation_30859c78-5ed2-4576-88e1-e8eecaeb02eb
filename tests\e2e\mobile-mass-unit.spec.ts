import { test, expect } from '@playwright/test'
import { login, setupMobile } from '../fixtures/auth'

test.describe('Mobile Mass Unit Preference', () => {
  test.beforeEach(async ({ page }) => {
    await setupMobile(page)
  })

  test('should respect user mass unit preference in SetScreen', async ({
    page,
  }) => {
    // Login and go to workout
    await login(page)
    await page.waitForURL('**/workout')

    // Navigate to first exercise
    await page.getByTestId('exercise-0').click()
    await page.waitForURL('**/workout/exercise/**')

    // Check that weight displays use the correct unit
    // The mock user data has MassUnit: 'lbs'
    await expect(page.getByText(/lbs/)).toBeVisible()

    // Check the weight input shows lbs
    const weightInput = page.getByLabel('Weight')
    await expect(weightInput).toBeVisible()

    // Check that previous performance shows lbs
    const previousPerf = page.getByText(/Last:.*lbs/)
    if (await previousPerf.isVisible()) {
      await expect(previousPerf).toContainText('lbs')
    }
  })

  test('should handle null Weight safely', async ({ page }) => {
    // Login and go to workout
    await login(page)
    await page.waitForURL('**/workout')

    // Navigate to exercise
    await page.getByTestId('exercise-0').click()
    await page.waitForURL('**/workout/exercise/**')

    // Page should load without errors even if some weights are null
    await expect(page).not.toHaveTitle(/error/i)

    // Check that the UI renders properly
    await expect(page.getByRole('button', { name: /save set/i })).toBeVisible()
  })
})
